entrypoint = "bash preview.sh"
modules = ["nodejs-22", "python-3.11"]
hidden = [".config", "package-lock.json"]

[gitHubImport]
requiredFiles = [".replit", "replit.nix", "package.json", "package-lock.json"]

[nix]
channel = "stable-24_11"

[deployment]
run = ["bash", "preview.sh"]
deploymentTarget = "autoscale"
ignorePorts = false
externalPort = 3000
build = ["bash", "build.sh"]

[workflows]
runButton = "Flask Server"

[[workflows.workflow]]
name = "Flask Server"
author = 15722197
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "sh preview.sh"

[[ports]]
localPort = 3000
externalPort = 3000

[[ports]]
localPort = 5000
externalPort = 80

[[ports]]
localPort = 5173
externalPort = 5173
