import React from "react";
import type { ProtectedFormData } from "../../types";

interface ProtectedFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (formData: ProtectedFormData) => void;
}

const ProtectedForm: React.FC<ProtectedFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Gather form data
    const formData = {
      /* protectedAmount, eligibleLeagues */
    };
    onSubmit(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 p-6 rounded-xl shadow-2xl w-full max-w-md">
        <h2 className="text-2xl font-bold text-[#58C612] mb-6">
          New Protected Play Promo
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <p className="text-gray-400">
            Protected Play form fields will go here.
          </p>
          {/* Placeholder for Protected Amount: <input type="number" id="protectedAmount" min="1"> */}
          {/* Placeholder for Eligible Leagues checkboxes */}
          <div className="flex justify-end space-x-3 mt-8">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-[#58C612] hover:bg-[#58C612] text-black font-bold rounded-md transition-colors"
            >
              Submit Protected Play
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProtectedForm;
