import React from "react";
import type { BoostFormData } from "../../types";

interface BoostFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (formData: BoostFormData) => void;
}

const BoostForm: React.FC<BoostFormProps> = ({ isOpen, onClose, onSubmit }) => {
  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Gather form data
    const formData = {
      /* boostPercentage, requiredPicks, sameSport */
    };
    onSubmit(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 p-6 rounded-xl shadow-2xl w-full max-w-md">
        <h2 className="text-2xl font-bold text-[#58C612] mb-6">
          New Boost Promo
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <p className="text-gray-400">Boost form fields will go here.</p>
          {/* Placeholder for Boost %: <input type="number" id="boostPercentage" min="1" max="100"> */}
          {/* Placeholder for Required Picks: <input type="number" id="requiredPicks" min="1"> */}
          {/* Placeholder for <input type="checkbox" id="sameSport"> Same Sport Restriction */}
          <div className="flex justify-end space-x-3 mt-8">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-green-500 hover:bg-green-600 text-black font-bold rounded-md transition-colors"
            >
              Submit Boost
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BoostForm;
