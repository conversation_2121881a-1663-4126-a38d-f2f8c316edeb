import React, { useState, useMemo } from "react";
import Header from "../Header";
import PicksView from "../PicksView";
import HandicappersView from "../HandicappersView";
import type { Pick, HandicapperPick, Handicapper } from "../../types";

type ViewType = "picks" | "handicappers";

interface Pick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence: number;
  expertCount: number;
  additionalExperts: number;
  handicapperNames: string[];
}

interface HandicapperPick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
}

interface Handicapper {
  id: number;
  name: string;
  sports: string;
  rating: number;
  accuracy: string;
  profileImage: string;
  picks: HandicapperPick[];
}

interface ViewContainerProps {
  onBackToHome: () => void;
  picks: Pick[];
  handicappers: Handicapper[];
}

function ViewContainer({
  onBackToHome,
  picks,
  handicappers,
}: ViewContainerProps) {
  const [activeView, setActiveView] = useState<ViewType>("picks");
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearchChange = (query: string) => {
    console.log("ViewContainer: handleSearchChange called with:", query);
    setSearchQuery(query);
  };

  const filteredPicks = useMemo(() => {
    const query = searchQuery.toLowerCase();
    console.log(
      "ViewContainer: filteredPicks useMemo running with query:",
      query
    );
    if (!query) return picks;

    return picks.filter((pick) => {
      return (
        (pick.playerName && pick.playerName.toLowerCase().includes(query)) ||
        (pick.playerNumber &&
          pick.playerNumber.toString().toLowerCase().includes(query)) ||
        (pick.betType && pick.betType.toLowerCase().includes(query)) ||
        (pick.gameInfo && pick.gameInfo.toLowerCase().includes(query)) ||
        (pick.handicapperNames &&
          pick.handicapperNames.some((name) =>
            name.toLowerCase().includes(query)
          ))
      );
    });
  }, [picks, searchQuery]);

  const filteredHandicappers = useMemo(() => {
    const query = searchQuery.toLowerCase();
    console.log(
      "ViewContainer: filteredHandicappers useMemo running with query:",
      query
    );
    if (!query) return handicappers;

    return handicappers.filter((handicapper) => {
      if (
        handicapper.name.toLowerCase().includes(query) ||
        handicapper.sports.toLowerCase().includes(query)
      ) {
        return true;
      }

      return handicapper.picks.some((pick) => {
        return (
          (pick.playerName && pick.playerName.toLowerCase().includes(query)) ||
          (pick.playerNumber &&
            pick.playerNumber.toString().toLowerCase().includes(query)) ||
          (pick.betType && pick.betType.toLowerCase().includes(query)) ||
          (pick.gameInfo && pick.gameInfo.toLowerCase().includes(query))
        );
      });
    });
  }, [handicappers, searchQuery]);

  return (
    <div className="min-h-screen bg-[#061844] text-white select-none">
      <Header
        onBackToHome={onBackToHome}
        onSearchChange={handleSearchChange}
        searchValue={searchQuery}
      />

      <div className="mx-auto px-4 py-4">
        <div className="flex items-center gap-4">
          <div className="mx-auto flex flex-col sm:flex-row items-center justify-center gap-4">
            <span className="text-white text-lg sm:text-xl md:text-[24px]">
              View By
            </span>
            <div className="flex rounded-lg overflow-hidden border-[#233e6c] border-4 ">
              <button
                onClick={() => setActiveView("picks")}
                className={`px-4 sm:px-6 py-2 text-sm sm:text-base font-semibold hover:cursor-pointer transition-colors ${
                  activeView === "picks"
                    ? "bg-[#233e6c] text-white"
                    : "text-gray-400 hover:text-white"
                }`}
              >
                Picks
              </button>
              <button
                onClick={() => setActiveView("handicappers")}
                className={`px-4 sm:px-6 py-2 text-sm sm:text-base font-semibold hover:cursor-pointer transition-colors ${
                  activeView === "handicappers"
                    ? "bg-[#233e6c] text-white"
                    : "text-gray-400 hover:text-white"
                }`}
              >
                Handicappers
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 pb-8">
        {activeView === "picks" ? (
          <PicksView picks={filteredPicks} />
        ) : (
          <HandicappersView handicappers={filteredHandicappers} />
        )}
      </div>
    </div>
  );
}

export default ViewContainer;
