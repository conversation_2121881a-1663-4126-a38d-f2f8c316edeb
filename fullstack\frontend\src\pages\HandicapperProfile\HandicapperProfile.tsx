import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, HiStar, HiTrash } from "react-icons/hi2";
import { useSidebar } from "../../contexts/SidebarContext";
import { useHandicapperProfile } from "../../contexts/HandicapperProfileContext";
import { useFavorites } from "../../contexts/FavoritesContext";
import { useAuth } from "../../contexts/AuthContext";
import { useHandicapperPicks } from "../../hooks/useTodaysEvents";
import { usePicks } from "../../contexts/PicksContext";
import { getConfidenceColor } from "../../utils/colorUtils";
import { IoShirtOutline } from "react-icons/io5";
import type { Pick } from "../../types";

// ===== DATE CONFIGURATION =====
// Set ENABLE_CUSTOM_DATE to true to use a specific date for testing
// Set CUSTOM_DATE to the date you want to test (YYYY-MM-DD format)
// When ENABLE_CUSTOM_DATE is false, it will use the current date
const ENABLE_CUSTOM_DATE = true; // Toggle this to enable/disable custom date
const CUSTOM_DATE = "2025-05-28"; // Set your test date here

interface Pick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence?: number;
}

interface HandicapperProfileData {
  id: number;
  name: string;
  accuracy: string;
  sports: string;
  rating?: number;
  picks: Pick[];
}

const HandicapperProfile: React.FC = () => {
  const { selectedId } = useHandicapperProfile();
  const { toggleSidebar, isOpen } = useSidebar();
  const { navigateToView } = useAuth();
  const { favoriteHandicappers } = useFavorites();
  const { getPicks, removePick, addPick, isPickSelected } = usePicks();
  const [profile, setProfile] = useState<HandicapperProfileData | null>(null);

  // Determine which date to use based on configuration (same as AddPicksPage)
  const targetDate = ENABLE_CUSTOM_DATE ? CUSTOM_DATE : undefined;

  // Use the new hook to get picks for this handicapper with the same date as AddPicksPage
  const {
    picks,
    handicapperName,
    loading: picksLoading,
    error: picksError,
  } = useHandicapperPicks(selectedId, targetDate);

  useEffect(() => {
    if (selectedId === null) return;

    // Log date configuration for debugging
    console.log("🏆 HandicapperProfile DATE CONFIG:");
    console.log("   - Custom date enabled:", ENABLE_CUSTOM_DATE);
    console.log(
      "   - Target date:",
      ENABLE_CUSTOM_DATE ? CUSTOM_DATE : "current date"
    );
    console.log("   - Passing to hook:", targetDate);

    // Try to get handicapper from favorites first
    const favoriteHandicapper = favoriteHandicappers.find(
      (h) => h.id === selectedId
    );

    let name = handicapperName;
    let accuracy = "N/A";

    // If it's in favorites, use the favorited data for name and accuracy
    if (favoriteHandicapper) {
      name = favoriteHandicapper.name;
      accuracy = favoriteHandicapper.accuracy;
    } else if (
      handicapperName &&
      handicapperName !== `Handicapper ${selectedId}`
    ) {
      // If we got a real name from the picks data, use that
      name = handicapperName;
      // Calculate rough accuracy based on number of picks (mock logic)
      const roughAccuracy = Math.min(95, 70 + Math.floor(picks.length * 2));
      accuracy = `${roughAccuracy}%`;
    }

    setProfile({
      id: selectedId,
      name: name,
      accuracy: accuracy,
      sports: "Multiple Sports",
      rating: Math.min(5, Math.max(3, Math.floor(parseFloat(accuracy) / 20))), // Convert accuracy to 3-5 star rating
      picks: picks,
    });
  }, [selectedId, favoriteHandicappers, handicapperName, picks, targetDate]);

  if (selectedId === null) {
    return (
      <div className="min-h-screen bg-[#061844] flex items-center justify-center text-white">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">
            No handicapper selected
          </h2>
          <button
            onClick={() => navigateToView("home")}
            className="px-4 py-2 bg-[#233e6c] hover:bg-[#1a2d54] rounded-lg transition-colors"
          >
            Return Home
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#061844] text-white">
      <header className="w-full bg-[#061844] p-4 border-b border-gray-700 flex items-center gap-4">
        {/* <button
          onClick={() => navigateToView("home")}
          className="p-2 bg-[#233e6c] rounded hover:bg-[#1a2d54] transition-colors hover:scale-105 cursor-pointer"
        >
          <HiArrowLeft className="w-5 h-5 text-white" />
        </button> */}
        {!isOpen && (
          <HiBars3
            className="w-8 h-8 text-white bg-[#233e6c] rounded p-1 cursor-pointer hover:bg-[#1a2d54] transition-colors"
            onClick={() => toggleSidebar()}
          />
        )}
        <h1 className="text-xl font-semibold ml-2">{"Handicapper Profile"}</h1>
        {ENABLE_CUSTOM_DATE && (
          <span className="text-sm text-gray-400 ml-auto">
            📅 {CUSTOM_DATE}
          </span>
        )}
      </header>

      <main className="p-6 max-w-6xl mx-auto">
        {picksLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#58C612] mx-auto mb-4"></div>
              <div className="text-gray-400">
                Loading handicapper profile...
              </div>
              {ENABLE_CUSTOM_DATE && (
                <div className="text-gray-500 text-sm mt-2">
                  📅 Using custom date: {CUSTOM_DATE}
                </div>
              )}
            </div>
          </div>
        ) : profile ? (
          (() => {
            // Filter recent picks to exclude ones that are already in user's picks
            // Use the profile name if available, otherwise fall back to handicapperName
            const currentHandicapperName = profile?.name || handicapperName;
            const availableRecentPicks = picks.filter(
              (pick) =>
                !isPickSelected("handicapper", pick.id, currentHandicapperName)
            );

            // Get user's picks from this specific handicapper (from PicksContext, not recent picks)
            const userPicksFromHandicapper = getPicks().filter(
              (pick) =>
                pick.sourceType === "handicapper" &&
                pick.handicapperName === currentHandicapperName
            );

            return (
              <>
                <div className="bg-[#233e6c] rounded-xl p-6 shadow-lg mb-8">
                  <div className="flex items-center gap-6 mb-6">
                    <div className="w-20 h-20 rounded-full bg-gray-700 flex items-center justify-center overflow-hidden">
                      <HiUser className="w-12 h-12 text-gray-400" />
                    </div>
                    <div className="flex-1">
                      <h2 className="text-white text-3xl font-bold mb-2">
                        {profile.name}
                      </h2>
                      <p className="text-white text-lg font-semibold mb-2">
                        {profile.sports}
                      </p>
                      <div className="flex items-center gap-4">
                        {profile.rating && (
                          <div className="flex items-center gap-2">
                            <div className="flex">
                              {Array.from({ length: 5 }).map((_, index) => (
                                <HiStar
                                  key={index}
                                  className={`w-5 h-5 ${
                                    index < profile.rating!
                                      ? "text-yellow-400"
                                      : "text-gray-600"
                                  }`}
                                />
                              ))}
                            </div>
                          </div>
                        )}
                        <span className="text-gray-300 text-lg font-medium">
                          {profile.accuracy} Accuracy
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* My Picks from this Handicapper Section */}
                <div>
                  <h3 className="text-2xl font-bold mb-6">
                    My Picks from {profile.name} (
                    {userPicksFromHandicapper.length})
                  </h3>
                  {userPicksFromHandicapper.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                      {userPicksFromHandicapper.map((userPick) => (
                        <div
                          key={userPick.id}
                          className="bg-[#233e6c] rounded-lg p-4 shadow-lg hover:shadow-xl hover:scale-[102%] transition-all duration-300 cursor-pointer"
                        >
                          <div className="flex flex-col items-center mb-3">
                            <div
                              className="w-14 h-14 rounded-full mb-3 flex items-center justify-center bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden"
                              style={{
                                border: `2px solid ${getConfidenceColor(
                                  userPick.confidence || 75
                                )}`,
                              }}
                            >
                              <IoShirtOutline
                                className="w-10 h-10 absolute"
                                style={{
                                  color: getConfidenceColor(
                                    userPick.confidence || 75
                                  ),
                                }}
                              />
                              <div className="text-white font-bold text-sm z-10 relative">
                                {userPick.playerNumber}
                              </div>
                            </div>
                            <h4 className="font-semibold mb-1 text-center line-clamp-2 text-white">
                              {userPick.playerName}
                            </h4>
                            <p
                              className="text-sm font-medium mb-2 text-center"
                              style={{
                                color: getConfidenceColor(
                                  userPick.confidence || 75
                                ),
                              }}
                            >
                              {userPick.betType}
                            </p>
                          </div>
                          <p className="text-gray-400 text-xs line-clamp-3 text-center">
                            {userPick.gameInfo}
                          </p>
                          {userPick.confidence && (
                            <div className="mt-3 text-center">
                              <span
                                className="text-xs font-semibold px-2 py-1 rounded"
                                style={{
                                  color: getConfidenceColor(
                                    userPick.confidence
                                  ),
                                }}
                              >
                                {userPick.confidence}% Confidence
                              </span>
                            </div>
                          )}
                          <div className="mt-4 text-center">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                removePick(userPick.id);
                              }}
                              className="p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all duration-300 hover:scale-105 shadow-md hover:shadow-lg hover:cursor-pointer"
                              title="Remove from my picks"
                            >
                              <HiTrash className="w-6 h-6 hover:cursor-pointer" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <p className="text-gray-400 text-lg">
                        You haven't added any picks from {profile.name} yet.
                      </p>
                      <p className="text-gray-500 text-sm mt-2">
                        Add picks from their Recent Picks section below to see
                        them here.
                      </p>
                    </div>
                  )}
                </div>

                <div className="mt-12">
                  <h3 className="text-2xl font-bold mb-6">
                    Recent Picks (
                    {picksLoading ? "..." : availableRecentPicks.length})
                  </h3>
                  {picksError ? (
                    <div className="text-center py-12">
                      <p className="text-red-400 text-lg mb-2">
                        Error loading picks: {picksError}
                      </p>
                      <button
                        onClick={() => window.location.reload()}
                        className="px-4 py-2 bg-[#233e6c] hover:bg-[#1a2d54] rounded-lg transition-colors"
                      >
                        Retry
                      </button>
                    </div>
                  ) : availableRecentPicks &&
                    availableRecentPicks.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                      {availableRecentPicks.map((pick) => (
                        <div
                          key={pick.id}
                          className="bg-[#233e6c] rounded-lg p-4 shadow-lg hover:shadow-xl hover:scale-[102%] transition-all duration-300 cursor-pointer"
                        >
                          <div className="flex flex-col items-center mb-3">
                            <div
                              className="w-14 h-14 rounded-full mb-3 flex items-center justify-center bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden"
                              style={{
                                border: `2px solid ${getConfidenceColor(
                                  pick.confidence || 75
                                )}`,
                              }}
                            >
                              <IoShirtOutline
                                className="w-10 h-10 absolute"
                                style={{
                                  color: getConfidenceColor(
                                    pick.confidence || 75
                                  ),
                                }}
                              />
                              <div className="text-white font-bold text-sm z-10 relative">
                                {pick.playerNumber}
                              </div>
                            </div>
                            <h4 className="font-semibold mb-1 text-center line-clamp-2 text-white">
                              {pick.playerName}
                            </h4>
                            <p
                              className="text-sm font-medium mb-2 text-center"
                              style={{
                                color: getConfidenceColor(
                                  pick.confidence || 75
                                ),
                              }}
                            >
                              {pick.betType}
                            </p>
                          </div>
                          <p className="text-gray-400 text-xs line-clamp-3 text-center">
                            {pick.gameInfo}
                          </p>
                          {pick.confidence && (
                            <div className="mt-3 text-center">
                              <span
                                className="text-xs font-semibold px-2 py-1 rounded"
                                style={{
                                  color: getConfidenceColor(pick.confidence),
                                }}
                              >
                                {pick.confidence}% Confidence
                              </span>
                            </div>
                          )}
                          <div className="mt-4 text-center">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                // Add the pick to user's selection
                                addPick({
                                  sourceType: "handicapper",
                                  sourceId: pick.id,
                                  playerName:
                                    pick.playerName || "Unknown Player",
                                  playerNumber: pick.playerNumber || "?",
                                  betType: pick.betType || "Standard Bet",
                                  gameInfo:
                                    pick.gameInfo || "Game info unavailable",
                                  confidence: pick.confidence || 75,
                                  handicapperName: currentHandicapperName,
                                });
                              }}
                              className="cursor-pointer shadow-md hover:shadow-lg transition-all duration-300 ease-linear text-sm font-bold px-4 py-2 rounded-lg whitespace-nowrap bg-white hover:bg-gray-300 text-[#061844]"
                            >
                              Add to List
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <p className="text-gray-400 text-lg">
                        No recent picks available for this handicapper.
                      </p>
                      {ENABLE_CUSTOM_DATE && (
                        <p className="text-gray-500 text-sm mt-2">
                          📅 Checked date: {CUSTOM_DATE}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </>
            );
          })()
        ) : (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-gray-400">
                Handicapper profile not found.
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default HandicapperProfile;
