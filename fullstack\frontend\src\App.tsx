import React from "react";
import HomePage from "./pages/HomePage/HomePage.tsx";
import Login from "./pages/Login/Login.tsx";
import { useAuth } from "./contexts/AuthContext.tsx";
import AddPicksPage from "./pages/AddPicksPage/AddPicksPage.tsx";
import Sidebar from "./components/Sidebar";
import HandicapperProfile from "./pages/HandicapperProfile";

function App() {
  const { currentView, login } = useAuth();

  let page: React.ReactNode;
  switch (currentView) {
    case "login":
      page = <Login performLogin={login} />;
      break;
    case "home":
      page = <HomePage />;
      break;
    case "addPicks":
      page = <AddPicksPage />;
      break;
    case "handicapperProfile":
      page = <HandicapperProfile />;
      break;
    default:
      page = <Login performLogin={login} />;
  }

  return (
    <>
      <Sidebar />
      {page}
    </>
  );
}

export default App;
